This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.8.3)  3 AUG 2025 23:49
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**c:/Users/<USER>/Desktop/PPT/CSC8099/Example0-100/Paper/thesis_outline_acm
(c:/Users/<USER>/Desktop/PPT/CSC8099/Example0-100/Paper/thesis_outline_acm.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/acmart/acmart.cls
Document Class: acmart 2024/12/28 v2.12 Typesetting articles for the Association for Computing Machinery
(d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/xkeyval/xkeyval.tex (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks17
\XKV@tempa@toks=\toks18
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/xkeyval/keyval.tex))
\XKV@depth=\count192
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/xstring/xstring.sty (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/xstring/xstring.tex
\xs_counta=\count193
\xs_countb=\count194
)
Package: xstring 2023/08/22 v1.86 String manipulations (CT)
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)
Package acmart Info: Not using screen mode on input line 76.
Package acmart Info: Using breaking urls on hyphens on input line 84.
Package acmart Info: Requiring acmthm on input line 92.
Package acmart Info: Not using review mode on input line 101.
Package acmart Info: Not using authorversion mode on input line 109.
Package acmart Info: Not using nonacm mode on input line 121.
Package acmart Info: Explicitly selecting natbib mode on input line 137.
Package acmart Info: Not using anonymous mode on input line 145.
Package acmart Info: Not using timestamp mode on input line 153.
Package acmart Info: Not using authordraft mode on input line 163.
Class acmart Info: Using format acmsmall, number 1 on input line 178.
Class acmart Info: Using fontsize 10pt on input line 280.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/amscls/amsart.cls
Document Class: amsart 2020/05/29 v2.20.6
\linespacing=\dimen141
\normalparindent=\dimen142
\normaltopskip=\skip49
(d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen143
)) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen144
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count195
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count196
\leftroot@=\count197
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count198
\DOTSCASE@=\count199
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen145
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count266
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count267
\dotsspace@=\muskip17
\c@parentequation=\count268
\dspbrk@lvl=\count269
\tag@help=\toks20
\row@=\count270
\column@=\count271
\maxfields@=\count272
\andhelp@=\toks21
\eqnshift@=\dimen146
\alignsep@=\dimen147
\tagshift@=\dimen148
\tagwidth@=\dimen149
\totwidth@=\dimen150
\lineht@=\dimen151
\@envbody=\toks22
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 397.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
\copyins=\insert252
\abstractbox=\box54
\listisep=\skip53
\c@part=\count273
\c@section=\count274
\c@subsection=\count275
\c@subsubsection=\count276
\c@paragraph=\count277
\c@subparagraph=\count278
\c@figure=\count279
\c@table=\count280
\abovecaptionskip=\skip54
\belowcaptionskip=\skip55
\captionindent=\dimen152
\thm@style=\toks24
\thm@bodyfont=\toks25
\thm@headfont=\toks26
\thm@notefont=\toks27
\thm@headpunct=\toks28
\thm@preskip=\skip56
\thm@postskip=\skip57
\thm@headsep=\skip58
\dth@everypar=\toks29
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2025/02/11 v3.2a Micro-typographical refinements (RS)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count281
)
\MT@toks=\toks30
\MT@tempbox=\box55
\MT@count=\count282
LaTeX Info: Redefining \noprotrusionifhmode on input line 1087.
LaTeX Info: Redefining \leftprotrusion on input line 1088.
\MT@prot@toks=\toks31
LaTeX Info: Redefining \rightprotrusion on input line 1107.
LaTeX Info: Redefining \textls on input line 1449.
\MT@outer@kern=\dimen153
LaTeX Info: Redefining \microtypecontext on input line 2053.
LaTeX Info: Redefining \textmicrotypecontext on input line 2070.
\MT@listname@count=\count283
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/microtype/microtype-xetex.def
File: microtype-xetex.def 2025/02/11 v3.2a Definitions specific to xetex (RS)
LaTeX Info: Redefining \lsstyle on input line 443.
LaTeX Info: Redefining \lslig on input line 451.
\MT@outer@space=\skip59
)
Package microtype Info: Loading configuration file microtype.cfg.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2025/02/11 v3.2a microtype main configuration file (RS)
)
LaTeX Info: Redefining \microtypesetup on input line 3065.
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen154
\lightrulewidth=\dimen155
\cmidrulewidth=\dimen156
\belowrulesep=\dimen157
\belowbottomsep=\dimen158
\aboverulesep=\dimen159
\abovetopsep=\dimen160
\cmidrulesep=\dimen161
\cmidrulekern=\dimen162
\defaultaddspace=\dimen163
\@cmidla=\count284
\@cmidlb=\count285
\@aboverulesep=\dimen164
\@belowrulesep=\dimen165
\@thisruleclass=\count286
\@lastruleclass=\count287
\@thisrulewidth=\dimen166
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/totpages/totpages.sty
Package: totpages 2005/09/19 v2.00 Totpages Package (muewi)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/everyshi/everyshi.sty
Package: everyshi 2020/11/18 v4.00 EveryShipout Package
)) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/environ/environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/trimspaces/trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))
\@ACM@acmcp@delta=\dimen167
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip60
\bibsep=\skip61
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count288
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count289
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen168
\Hy@linkcounter=\count290
\Hy@pagecounter=\count291
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count292
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `bookmarksnumbered' set `true' on input line 4040.
Package hyperref Info: Option `unicode' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count293
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen169
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count294
\Field@Width=\dimen170
\Fld@charsize=\dimen171
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count295
\c@Item=\count296
\c@Hfootnote=\count297
)
Package hyperref Info: Driver (autodetected): hxetex.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-11-05 v7.01l Hyperref driver for XeTeX
\pdfm@box=\box56
\c@Hy@AnnotLevel=\count298
\HyField@AnnotCount=\count299
\Fld@listcount=\count300
\c@bookmark@seq@number=\count301
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip62
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/hyperxmp/hyperxmp.sty
Package: hyperxmp 2024/03/17 v5.13 Store hyperref metadata in XMP format
\hyxmp@aep@toks=\toks32
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/ifmtarg/ifmtarg.sty
Package: ifmtarg 2018/04/16 v1.2b check for an empty argument
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\@hyxmp@count=\count302
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/oberdiek/ifdraft.sty
Package: ifdraft 2016/05/16 v1.4 Detect class options draft and final (HO)
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/iftex/ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
)) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen172
\Gin@req@width=\dimen173
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count303
\Gm@cntv=\count304
\c@Gm@tempcnt=\count305
\Gm@bindingoffset=\dimen174
\Gm@wd@mp=\dimen175
\Gm@odd@mp=\dimen176
\Gm@even@mp=\dimen177
\Gm@layoutwidth=\dimen178
\Gm@layoutheight=\dimen179
\Gm@layouthoffset=\dimen180
\Gm@layoutvoffset=\dimen181
\Gm@dimlist=\toks33
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/ncctools/manyfoot.sty
Package: manyfoot 2019/08/03 v1.11 Many Footnote Levels Package (NCC)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/ncctools/nccfoots.sty
Package: nccfoots 2005/02/03 v1.2 NCC Footnotes Package (NCC)
)
\MFL@columnwidth=\dimen182
)
\footinsauthorsaddresses=\insert251
\c@footnoteauthorsaddresses=\count306
\footinscopyrightpermission=\insert250
\c@footnotecopyrightpermission=\count307
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/unicode-math/unicode-math.sty (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count308
\l__pdf_internal_box=\box57
\g__pdf_backend_annotation_int=\count309
\g__pdf_backend_link_int=\count310
))
Package: unicode-math 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/unicode-math/unicode-math-xetex.sty
Package: unicode-math-xetex 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2024-08-16 LaTeX2e option processing using LaTeX3 keys
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec.sty
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count311
\l__fontspec_language_int=\count312
\l__fontspec_strnum_int=\count313
\l__fontspec_tmp_int=\count314
\l__fontspec_tmpa_int=\count315
\l__fontspec_tmpb_int=\count316
\l__fontspec_tmpc_int=\count317
\l__fontspec_em_int=\count318
\l__fontspec_emdef_int=\count319
\l__fontspec_strong_int=\count320
\l__fontspec_strongdef_int=\count321
\l__fontspec_tmpa_dim=\dimen183
\l__fontspec_tmpb_dim=\dimen184
\l__fontspec_tmpc_dim=\dimen185
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\g__um_fam_int=\count322
\g__um_fonts_used_int=\count323
\l__um_primecount_int=\count324
\g__um_primekern_muskip=\muskip19
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/unicode-math/unicode-math-table.tex)))

Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             Font family 'libertinusmath-regular.otf(0)' created for
(fontspec)             font 'libertinusmath-regular.otf' with options
(fontspec)             [BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,Scale={MatchUppercase}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[1.037991503334663]"[libertinusmath-regular.otf]/OT:script=math;language=dflt;"

LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(0)/m/n' will be
(Font)              scaled to size 10.37994pt on input line 868.

Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             Font family 'libertinusmath-regular.otf(1)' created for
(fontspec)             font 'libertinusmath-regular.otf' with options
(fontspec)             [BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,SizeFeatures={{Size=9-},{Size=7-9,Font=libertinusmath-regular.otf,Style=MathScript},{Size=-7,Font=libertinusmath-regular.otf,Style=MathScriptScript}},Scale={MatchUppercase}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <9->s*[1.037991503334663]"[libertinusmath-regular.otf]/OT:script=math;language=dflt;"<7-9>s*[1.037991503334663]"[libertinusmath-regular.otf]/OT:script=math;language=dflt;+ssty=0;"<-7>s*[1.037991503334663]"[libertinusmath-regular.otf]/OT:script=math;language=dflt;+ssty=1;"

LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(1)/m/n' will be
(Font)              scaled to size 10.37994pt on input line 868.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 868.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/libertinusmath-regular.otf(1)/m/n on input line 868.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 868.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/libertinusmath-regular.otf(1)/b/n on input line 868.

Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.038095302484996.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.038095302484996.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.038095302484996.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.038095302484996.


Package fontspec Info: 
(fontspec)             Font family 'libertinusmath-regular.otf(2)' created for
(fontspec)             font 'libertinusmath-regular.otf' with options
(fontspec)             [BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,SizeFeatures={{Size=9-},{Size=7-9,Font=libertinusmath-regular.otf,Style=MathScript},{Size=-7,Font=libertinusmath-regular.otf,Style=MathScriptScript}},Scale={MatchUppercase},ScaleAgain=1.0001,FontAdjustment={\fontdimen
(fontspec)             8\font =6.02037pt\relax \fontdimen 9\font
(fontspec)             =4.98238pt\relax \fontdimen 10\font =4.87857pt\relax
(fontspec)             \fontdimen 11\font =7.26596pt\relax \fontdimen 12\font
(fontspec)             =4.98238pt\relax \fontdimen 13\font =3.89249pt\relax
(fontspec)             \fontdimen 14\font =3.89249pt\relax \fontdimen 15\font
(fontspec)             =3.21779pt\relax \fontdimen 16\font =2.1798pt\relax
(fontspec)             \fontdimen 17\font =2.1798pt\relax \fontdimen 18\font
(fontspec)             =2.38739pt\relax \fontdimen 19\font =0.4671pt\relax
(fontspec)             \fontdimen 22\font =2.69879pt\relax \fontdimen 20\font
(fontspec)             =0pt\relax \fontdimen 21\font =0pt\relax }].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <9->s*[1.038095302484996]"[libertinusmath-regular.otf]/OT:script=math;language=dflt;"<7-9>s*[1.038095302484996]"[libertinusmath-regular.otf]/OT:script=math;language=dflt;+ssty=0;"<-7>s*[1.038095302484996]"[libertinusmath-regular.otf]/OT:script=math;language=dflt;+ssty=1;"

LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `normal' on input line 868.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> TU/libertinusmath-regular.otf(2)/m/n on input line 868.
LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `bold' on input line 868.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> TU/libertinusmath-regular.otf(2)/b/n on input line 868.

Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.03788770418433.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.03788770418433.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.03788770418433.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.037991503334663.


Package fontspec Info: 
(fontspec)             libertinusmath-regular scale = 1.03788770418433.


Package fontspec Info: 
(fontspec)             Font family 'libertinusmath-regular.otf(3)' created for
(fontspec)             font 'libertinusmath-regular.otf' with options
(fontspec)             [BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,SizeFeatures={{Size=9-},{Size=7-9,Font=libertinusmath-regular.otf,Style=MathScript},{Size=-7,Font=libertinusmath-regular.otf,Style=MathScriptScript}},Scale={MatchUppercase},ScaleAgain=0.9999,FontAdjustment={\fontdimen
(fontspec)             8\font =0.6747pt\relax \fontdimen 9\font
(fontspec)             =1.55699pt\relax \fontdimen 10\font =1.55699pt\relax
(fontspec)             \fontdimen 11\font =2.59499pt\relax \fontdimen 12\font
(fontspec)             =6.43556pt\relax \fontdimen 13\font =0pt\relax }].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <9->s*[1.03788770418433]"[libertinusmath-regular.otf]/OT:script=math;language=dflt;"<7-9>s*[1.03788770418433]"[libertinusmath-regular.otf]/OT:script=math;language=dflt;+ssty=0;"<-7>s*[1.03788770418433]"[libertinusmath-regular.otf]/OT:script=math;language=dflt;+ssty=1;"

LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 868.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> TU/libertinusmath-regular.otf(3)/m/n on input line 868.
LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 868.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> TU/libertinusmath-regular.otf(3)/b/n on input line 868.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/libertine/libertine.sty
Package: libertine 2024/04/23 (Bob Tennent) Supports Libertine and Biolinum fonts for all LaTeX engines.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/generic/iftex/ifxetex.sty
Package: ifxetex 2019/10/25 v0.7 ifxetex legacy package. Use iftex instead.
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
)

Package fontspec Info: 
(fontspec)             LinLibertine_R scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_R scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_R scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RI scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RI scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RBI scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RBI scale = 1.


Package fontspec Info: 
(fontspec)             Font family 'LinLibertine(0)' created for font
(fontspec)             'LinLibertine' with options [Ligatures = TeX,Extension
(fontspec)             =
(fontspec)             .otf,SmallCapsFeatures={Letters=SmallCaps},Ligatures=TeX,Numbers
(fontspec)             = {Monospaced,Lining},Scale = 1,UprightFont =
(fontspec)             *_R,ItalicFont = *_RI,BoldFont = *_RB,BoldItalicFont =
(fontspec)             *_RBI].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_R.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_R.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RB.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RB.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RBI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RBI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"


Package fontspec Info: 
(fontspec)             LinLibertine_R scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_R scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_R scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RI scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RI scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RBI scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RBI scale = 1.


Package fontspec Info: 
(fontspec)             Font family 'LinLibertine(1)' created for font
(fontspec)             'LinLibertine' with options [Ligatures = TeX,Extension
(fontspec)             = .otf,SmallCapsFeatures={Letters=SmallCaps},Numbers =
(fontspec)             {Monospaced,Lining},Scale = 1,UprightFont =
(fontspec)             *_R,ItalicFont = *_RI,BoldFont = *_RB,BoldItalicFont =
(fontspec)             *_RBI].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_R.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_R.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RB.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RB.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RBI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RBI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"

LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  TU/libertinusmath-regular.otf(1)/m/n --> TU/LinLibertine(1)/m/n on input line 307.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/LinLibertine(1)/m/it on input line 307.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/LinLibertine(1)/b/n on input line 307.
LaTeX Font Info:    Font shape `TU/LinLibertine(0)/m/n' will be
(Font)              scaled to size 10.0pt on input line 307.

Package fontspec Info: 
(fontspec)             LinBiolinum_R scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_R scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_R scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RI scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RI scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RBO scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RBO scale = 1.


Package fontspec Info: 
(fontspec)             Font family 'LinBiolinum(0)' created for font
(fontspec)             'LinBiolinum' with options [Ligatures = TeX,Extension =
(fontspec)             .otf,SmallCapsFeatures={Letters=SmallCaps},Ligatures=TeX,Numbers
(fontspec)             = {Monospaced, Lining},Scale = 1,UprightFont =
(fontspec)             *_R,ItalicFont = *_RI,BoldFont = *_RB,BoldItalicFont =
(fontspec)             *_RBO].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_R.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_R.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RB.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RB.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RBO.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RBO.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"


Package fontspec Info: 
(fontspec)             LinBiolinum_R scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_R scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_R scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RI scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RI scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RBO scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RBO scale = 1.


Package fontspec Info: 
(fontspec)             Font family 'LinBiolinum(1)' created for font
(fontspec)             'LinBiolinum' with options [Ligatures = TeX,Extension =
(fontspec)             .otf,SmallCapsFeatures={Letters=SmallCaps},Numbers =
(fontspec)             {Monospaced, Lining},Scale = 1,UprightFont =
(fontspec)             *_R,ItalicFont = *_RI,BoldFont = *_RB,BoldItalicFont =
(fontspec)             *_RBO].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_R.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_R.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RB.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RB.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RBO.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RBO.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"

LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/LinBiolinum(1)/m/n on input line 318.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/LinBiolinum(1)/b/n on input line 318.

Package fontspec Info: 
(fontspec)             LinLibertine_R scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RZ scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RZ scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RZ scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RZI scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RZI scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RBI scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RBI scale = 1.


Package fontspec Info: 
(fontspec)             Font family 'LinLibertine(2)' created for font
(fontspec)             'LinLibertine' with options [Ligatures = TeX,Extension
(fontspec)             = .otf,SmallCapsFeatures={Letters=SmallCaps},Numbers =
(fontspec)             { Monospaced, Lining},Scale = 1,UprightFont =
(fontspec)             *_RZ,ItalicFont = *_RZI,BoldFont = *_RB,BoldItalicFont
(fontspec)             = *_RBI].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RZ.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RZ.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RB.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RB.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RZI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RZI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RBI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RBI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"


Package fontspec Info: 
(fontspec)             LinLibertine_R scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_R scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_R scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RI scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RI scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RBI scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_RBI scale = 1.


Package fontspec Info: 
(fontspec)             Font family 'LinLibertine(3)' created for font
(fontspec)             'LinLibertine' with options [Ligatures = TeX,Extension
(fontspec)             = .otf,SmallCapsFeatures={Letters=SmallCaps},Numbers =
(fontspec)             {Monospaced,OldStyle},Scale = 1,UprightFont =
(fontspec)             *_R,ItalicFont = *_RI,BoldFont = *_RB,BoldItalicFont =
(fontspec)             *_RBI].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_R.otf]/OT:script=latn;language=dflt;+tnum;+onum;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_R.otf]/OT:script=latn;language=dflt;+tnum;+onum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RB.otf]/OT:script=latn;language=dflt;+tnum;+onum;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RB.otf]/OT:script=latn;language=dflt;+tnum;+onum;+smcp;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RI.otf]/OT:script=latn;language=dflt;+tnum;+onum;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RI.otf]/OT:script=latn;language=dflt;+tnum;+onum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RBI.otf]/OT:script=latn;language=dflt;+tnum;+onum;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_RBI.otf]/OT:script=latn;language=dflt;+tnum;+onum;+smcp;mapping=tex-text;"


Package fontspec Info: 
(fontspec)             LinLibertine_R scale = 1.


Package fontspec Info: 
(fontspec)             Font family 'LinLibertine(4)' created for font
(fontspec)             'LinLibertine' with options [Ligatures = TeX,Extension
(fontspec)             = .otf,SmallCapsFeatures={Letters=SmallCaps},Numbers =
(fontspec)             {Monospaced,Lining},UprightFont = *_DR,ItalicFont =
(fontspec)             *_RI,BoldFont = *_RB,BoldItalicFont = *_RBI].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[LinLibertine_DR.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"[LinLibertine_RB.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.:
(fontspec)             <->"[LinLibertine_RB.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"[LinLibertine_RI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->"[LinLibertine_RI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.:
(fontspec)             <->"[LinLibertine_RBI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->"[LinLibertine_RBI.otf]/OT:script=latn;language=dflt;+tnum;+lnum;+smcp;mapping=tex-text;"


Package fontspec Info: 
(fontspec)             LinBiolinum_R scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_R scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_R scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_R scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RB scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RI scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RI scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RBO scale = 1.


Package fontspec Info: 
(fontspec)             LinBiolinum_RBO scale = 1.


Package fontspec Info: 
(fontspec)             Font family 'LinBiolinum(2)' created for font
(fontspec)             'LinBiolinum' with options [Ligatures = TeX,Extension =
(fontspec)             .otf,SmallCapsFeatures={Letters=SmallCaps},Numbers =
(fontspec)             {Monospaced, OldStyle},Scale = 1,UprightFont =
(fontspec)             *_R,ItalicFont = *_RI,BoldFont = *_RB,BoldItalicFont =
(fontspec)             *_RBO].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_R.otf]/OT:script=latn;language=dflt;+tnum;+onum;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_R.otf]/OT:script=latn;language=dflt;+tnum;+onum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RB.otf]/OT:script=latn;language=dflt;+tnum;+onum;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RB.otf]/OT:script=latn;language=dflt;+tnum;+onum;+smcp;mapping=tex-text;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RI.otf]/OT:script=latn;language=dflt;+tnum;+onum;mapping=tex-text;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RI.otf]/OT:script=latn;language=dflt;+tnum;+onum;+smcp;mapping=tex-text;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RBO.otf]/OT:script=latn;language=dflt;+tnum;+onum;mapping=tex-text;"
(fontspec)             - 'bold italic small caps'  (b/scit) with NFSS spec.:
(fontspec)             <->s*[1]"[LinBiolinum_RBO.otf]/OT:script=latn;language=dflt;+tnum;+onum;+smcp;mapping=tex-text;"


Package fontspec Info: 
(fontspec)             LinBiolinum_R scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_M scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_M scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_MB scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_MO scale = 1.


Package fontspec Info: 
(fontspec)             LinLibertine_MBO scale = 1.


Package fontspec Info: 
(fontspec)             Font family 'LinLibertine(5)' created for font
(fontspec)             'LinLibertine' with options [Extension =
(fontspec)             .otf,HyphenChar=None,Scale = 1,UprightFont =
(fontspec)             *_M,ItalicFont = *_MO,BoldFont = *_MB,BoldItalicFont =
(fontspec)             *_MBO].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_M.otf]/OT:script=latn;language=dflt;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_MB.otf]/OT:script=latn;language=dflt;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_MO.otf]/OT:script=latn;language=dflt;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.:
(fontspec)             <->s*[1]"[LinLibertine_MBO.otf]/OT:script=latn;language=dflt;"


Package fontspec Info: 
(fontspec)             Font family 'LinBiolinum_K(0)' created for font
(fontspec)             'LinBiolinum_K' with options [Extension =
(fontspec)             .otf,HyphenChar=None,BoldFont={},ItalicFont={},SmallCapsFont={}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[LinBiolinum_K.otf]/OT:script=latn;language=dflt;"


Package fontspec Info: 
(fontspec)             Font family 'LinLibertine_I(0)' created for font
(fontspec)             'LinLibertine_I' with options [Extension =
(fontspec)             .otf,HyphenChar=None,BoldFont={},ItalicFont={},SmallCapsFont={}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[LinLibertine_I.otf]/OT:script=latn;language=dflt;"

LaTeX Info: Redefining \oldstylenums on input line 475.
LaTeX Info: Redefining \liningnums on input line 476.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/libertine/LinLibertine_R.tex) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/libertine/LinBiolinum_R.tex) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/libertine/LinBiolinum_K.tex) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/libertine/LinLibertine_I.tex) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/libertine/LKey.tex)
LaTeX Info: Redefining \textsup on input line 552.
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/inconsolata/inconsolata.fontspec)

Package fontspec Info: 
(fontspec)             Font family 'inconsolata(0)' created for font
(fontspec)             'inconsolata' with options
(fontspec)             [WordSpace={1,0,0},HyphenChar=None,PunctuationSpace=WordSpace,Extension
(fontspec)             = .otf,UprightFont = Inconsolatazi4-Regular,BoldFont =
(fontspec)             Inconsolatazi4-Bold,StylisticSet=3].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[Inconsolatazi4-Regular.otf]/OT:script=latn;language=dflt;+ss03;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"[Inconsolatazi4-Bold.otf]/OT:script=latn;language=dflt;+ss03;"


Package fontspec Info: 
(fontspec)             Font family 'inconsolata(1)' created for font
(fontspec)             'inconsolata' with options [Extension =
(fontspec)             .otf,UprightFont = Inconsolatazi4-Regular,BoldFont =
(fontspec)             Inconsolatazi4-Bold,StylisticSet=3].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[Inconsolatazi4-Regular.otf]/OT:script=latn;language=dflt;+ss03;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"[Inconsolatazi4-Bold.otf]/OT:script=latn;language=dflt;+ss03;"

LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/inconsolata(1)/m/n on input line 870.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/inconsolata(1)/b/n on input line 870.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen186
\captionmargin=\dimen187
\caption@leftmargin=\dimen188
\caption@rightmargin=\dimen189
\caption@width=\dimen190
\caption@indent=\dimen191
\caption@parindent=\dimen192
\caption@hangindent=\dimen193
Package caption Info: AMS or SMF document class detected.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/caption/caption-ams-smf.sto
File: caption-ams-smf.sto 2020/08/22 v2.0 Adaption of the caption package to the AMS and SMF document classes (AR)
))
\c@caption@flags=\count325
\c@continuedfloat=\count326
Package caption Info: hyperref package is loaded.
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count327
\float@exts=\toks34
\float@box=\box58
\@float@everytoks=\toks35
\@floatcapt=\box59
)
\@float@every@sidebar=\toks36
\c@sidebar=\count328
\fulltextwidth=\dimen194
\@ACM@labelwidth=\dimen195
\listisep=\skip63
\num@authorgroups=\count329
\num@authors=\count330
\@ACM@badge@width=\skip64
\@ACM@title@width=\skip65
\@ACM@badge@skip=\skip66
Class acmart Info: Printing CCS on input line 1806.
Class acmart Info: Printing bibformat on input line 1806.
Class acmart Info: Printing folios on input line 1811.
Class acmart Info: Setting authorsperrow to 0 on input line 1816.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/comment/comment.sty
\CommentStream=\write3
 Excluding comment 'comment') Excluding comment 'CCSXML'
\c@@concepts=\count331
\mktitle@bx=\box60
\@ACM@acmcpbox=\box61
\@ACM@commabox=\box62
\author@bx=\box63
\author@bx@wd=\dimen196
\author@bx@sep=\skip67
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers
\f@nch@headwidth=\skip68
\f@nch@offset@elh=\skip69
\f@nch@offset@erh=\skip70
\f@nch@offset@olh=\skip71
\f@nch@offset@orh=\skip72
\f@nch@offset@elf=\skip73
\f@nch@offset@erf=\skip74
\f@nch@offset@olf=\skip75
\f@nch@offset@orf=\skip76
\f@nch@height=\skip77
\f@nch@footalignment=\skip78
\f@nch@widthL=\skip79
\f@nch@widthC=\skip80
\f@nch@widthR=\skip81
\@temptokenb=\toks37
) Special comment 'acks')
Class acmart Info: Using journal code jacm on input line 19.
Package hyperref Info: Option `pdfdisplaydoctitle' set `true' on input line 27.
\c@theorem=\count332
 Excluding comment 'screenonly' Include comment 'printonly' Include comment 'anonsuppress' (./thesis_outline_acm.aux)
\openout1 = `thesis_outline_acm.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 27.
LaTeX Font Info:    ... okay on input line 27.
LaTeX Font Info:    Font shape `TU/LinLibertine(0)/m/n' will be
(Font)              scaled to size 8.0pt on input line 27.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(1)/m/n' will be
(Font)              scaled to size 8.30396pt on input line 27.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(1)/m/n' will be
(Font)              scaled to size 6.22797pt on input line 27.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(1)/m/n' will be
(Font)              scaled to size 5.18997pt on input line 27.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(2)/m/n' will be
(Font)              scaled to size 8.30481pt on input line 27.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(2)/m/n' will be
(Font)              scaled to size 6.2286pt on input line 27.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(2)/m/n' will be
(Font)              scaled to size 5.1905pt on input line 27.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(3)/m/n' will be
(Font)              scaled to size 8.3031pt on input line 27.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(3)/m/n' will be
(Font)              scaled to size 6.22733pt on input line 27.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(3)/m/n' will be
(Font)              scaled to size 5.18944pt on input line 27.
LaTeX Font Info:    Trying to load font information for U+msa on input line 27.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 27.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Info: Redefining \microtypecontext on input line 27.
Package microtype Info: Applying patch `item' on input line 27.
Package microtype Info: Applying patch `toc' on input line 27.
Package microtype Info: Applying patch `eqnum' on input line 27.
Package microtype Info: Applying patch `footnote' on input line 27.
Package microtype Info: Applying patch `verbatim' on input line 27.
LaTeX Info: Redefining \microtypesetup on input line 27.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using default protrusion set `alltext'.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of spacing.
Package microtype Info: No adjustment of kerning.
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `LinLibertine' (encoding: TU).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.
Package hyperref Info: Link coloring OFF on input line 27.
 (./thesis_outline_acm.out) (./thesis_outline_acm.out)
\@outlinefile=\write4
\openout4 = `thesis_outline_acm.out'.


*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: custom
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: includehead includefoot twoside heightrounded 
* h-part:(L,W,R)=(46.0pt, 395.8225pt, 46.0pt)
* v-part:(T,H,B)=(58.0pt, 620.7pt, 44.0pt)
* \paperwidth=487.8225pt
* \paperheight=722.7pt
* \textwidth=395.8225pt
* \textheight=574.0pt
* \oddsidemargin=-26.26999pt
* \evensidemargin=-26.26999pt
* \topmargin=-14.26999pt
* \headheight=13.0pt
* \headsep=14.0pt
* \topskip=10.0pt
* \footskip=24.0pt
* \marginparwidth=24.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=7.0pt plus 11.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidetrue
* \@mparswitchtrue
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package caption Info: Begin \AtBeginDocument code.
Package caption Info: float package is loaded.
Package caption Info: End \AtBeginDocument code.
Excluding 'CCSXML' comment.
LaTeX Font Info:    Font shape `TU/LinLibertine(0)/m/n' will be
(Font)              scaled to size 14.4pt on input line 102.
LaTeX Font Info:    Font shape `TU/LinBiolinum(0)/m/n' will be
(Font)              scaled to size 14.4pt on input line 102.
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `LinBiolinum' (encoding: TU).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.
LaTeX Font Info:    Font shape `TU/LinBiolinum(0)/b/n' will be
(Font)              scaled to size 14.4pt on input line 102.
LaTeX Font Info:    Font shape `TU/LinLibertine(0)/m/n' will be
(Font)              scaled to size 10.95pt on input line 102.
LaTeX Font Info:    Font shape `TU/LinBiolinum(0)/m/n' will be
(Font)              scaled to size 10.95pt on input line 102.
LaTeX Font Info:    Font shape `TU/LinBiolinum(0)/m/n' will be
(Font)              scaled to size 9.0pt on input line 102.
LaTeX Font Info:    Font shape `TU/LinLibertine(0)/m/n' will be
(Font)              scaled to size 9.0pt on input line 102.
LaTeX Font Info:    Font shape `TU/LinLibertine(0)/b/n' will be
(Font)              scaled to size 9.0pt on input line 102.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(1)/m/n' will be
(Font)              scaled to size 9.34195pt on input line 102.
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `libertinusmath-regular.otf' (encoding: TU).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(2)/m/n' will be
(Font)              scaled to size 9.34291pt on input line 102.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(3)/m/n' will be
(Font)              scaled to size 9.34099pt on input line 102.
 (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/microtype/mt-msa.cfg
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
) (d:/TeXLive2025/texlive/2025/texmf-dist/tex/latex/microtype/mt-msb.cfg
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
)
LaTeX Font Info:    Font shape `TU/LinLibertine(0)/m/it' will be
(Font)              scaled to size 9.0pt on input line 102.
LaTeX Font Info:    Font shape `TU/LinBiolinum(0)/m/n' will be
(Font)              scaled to size 10.0pt on input line 107.
LaTeX Font Info:    Font shape `TU/LinBiolinum(0)/b/n' will be
(Font)              scaled to size 10.0pt on input line 107.
LaTeX Font Info:    Font shape `TU/LinLibertine(0)/b/n' will be
(Font)              scaled to size 10.0pt on input line 113.


Package natbib Warning: Citation `owasp2021' on page 1 undefined on input line 113.



[1.1]
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(2)/m/n' will be
(Font)              scaled to size 10.38101pt on input line 135.
LaTeX Font Info:    Font shape `TU/libertinusmath-regular.otf(3)/m/n' will be
(Font)              scaled to size 10.37888pt on input line 135.

Underfull \vbox (badness 10000) has occurred while \output is active []



LaTeX Font Info:    Font shape `TU/LinBiolinum(0)/m/n' will be
(Font)              scaled to size 8.0pt on input line 136.
[2.2]

Package natbib Warning: Citation `owasp2021' on page 3 undefined on input line 168.


Package natbib Warning: Citation `testing2020' on page 3 undefined on input line 173.


Underfull \vbox (badness 7397) has occurred while \output is active []




Package fancyhdr Warning: \headheight is too small (13.0pt): 
(fancyhdr)                Make it at least 18.58403pt, for example:
(fancyhdr)                \setlength{\headheight}{18.58403pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-5.58403pt}.

[3.3]

Package natbib Warning: Citation `bandit2020' on page 4 undefined on input line 178.


Package natbib Warning: Citation `zap2021' on page 4 undefined on input line 178.


Package natbib Warning: Citation `smith2022' on page 4 undefined on input line 180.


Package natbib Warning: Citation `johnson2023' on page 4 undefined on input line 180.

LaTeX Font Info:    Font shape `TU/LinBiolinum(0)/m/it' will be
(Font)              scaled to size 10.0pt on input line 207.


[4.4]


Package fancyhdr Warning: \headheight is too small (13.0pt): 
(fancyhdr)                Make it at least 18.58403pt, for example:
(fancyhdr)                \setlength{\headheight}{18.58403pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-5.58403pt}.

[5.5]

[6.6]
Overfull \hbox (10.6834pt too wide) in paragraph at lines 413--414
[]\TU/LinLibertine(0)/b/n/10 Methodological Contribution: \TU/LinLibertine(0)/m/n/10 We established a novel approach to implementation-independent
 []




Package fancyhd