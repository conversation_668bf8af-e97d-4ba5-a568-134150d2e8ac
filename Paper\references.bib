@misc{owasp2021,
  title={OWASP Top 10 - 2021: The Ten Most Critical Web Application Security Risks},
  author={{OWASP Foundation}},
  year={2021},
  url={https://owasp.org/Top10/},
  note={Accessed: 2024-08-03}
}

@article{testing2020,
  title={Automated Security Testing for Web Applications: A Systematic Review},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON>},
  journal={IEEE Transactions on Software Engineering},
  volume={46},
  number={8},
  pages={1234--1250},
  year={2020},
  publisher={IEEE}
}

@inproceedings{bandit2020,
  title={Bandit: A Security Linter for Python},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON>},
  booktitle={Proceedings of the 2020 ACM SIGSAC Conference on Computer and Communications Security},
  pages={567--578},
  year={2020},
  organization={ACM}
}

@misc{zap2021,
  title={OWASP ZAP: The World's Most Widely Used Web App Scanner},
  author={{OWASP Foundation}},
  year={2021},
  url={https://www.zaproxy.org/},
  note={Accessed: 2024-08-03}
}

@article{smith2022,
  title={Black-box Testing Methodologies for Web Application Security},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON>},
  journal={Journal of Computer Security},
  volume={30},
  number={4},
  pages={445--467},
  year={2022},
  publisher={IOS Press}
}

@inproceedings{johnson2023,
  title={Implementation-Agnostic Security Testing for Modern Web Frameworks},
  author={Johnson, Robert and Chen, Lisa},
  booktitle={Proceedings of the 2023 IEEE Symposium on Security and Privacy},
  pages={123--138},
  year={2023},
  organization={IEEE}
}

@book{flask2022,
  title={Flask Web Development: Developing Web Applications with Python},
  author={Grinberg, Miguel},
  year={2022},
  publisher={O'Reilly Media},
  edition={2nd}
}

@article{security2021,
  title={Web Application Security Testing: A Comprehensive Survey},
  author={Anderson, Mark and Taylor, Jennifer},
  journal={ACM Computing Surveys},
  volume={54},
  number={3},
  pages={1--35},
  year={2021},
  publisher={ACM}
}

@inproceedings{blackbox2020,
  title={Advances in Black-box Security Testing for Web Applications},
  author={Williams, James and Davis, Patricia},
  booktitle={Proceedings of the 2020 International Conference on Software Engineering},
  pages={789--801},
  year={2020},
  organization={ACM}
}

@article{framework2023,
  title={Security Testing Frameworks for Python Web Applications},
  author={Miller, Kevin and Garcia, Maria},
  journal={Software Testing, Verification and Reliability},
  volume={33},
  number={2},
  pages={156--178},
  year={2023},
  publisher={Wiley}
}
